# MS Calculator - Windows Deployment Guide

## Quick Start

1. **First-time setup**: Double-click `check_requirements.bat`
2. **Start application**: Double-click `start_app.bat`
3. **Access application**: Open browser to `http://localhost:8080`

## Detailed Setup Instructions

### Prerequisites
- Windows 10/11 or Windows Server
- Python 3.8+ installed and in PATH
- Internet connection for downloading dependencies

### Step-by-Step Deployment

1. **Extract/Clone the application** to a folder like `C:\Projects\ms_calculator`

2. **Run the setup script**:
   - Double-click `check_requirements.bat`
   - This will:
     - Check Python installation
     - Create a virtual environment
     - Install all required packages
     - Run tests to verify everything works

3. **Start the application**:
   - For production: Double-click `start_app.bat`
   - For development: Double-click `start_app_dev.bat`

4. **Access the application**:
   - Open your web browser
   - Navigate to `http://localhost:8080`

### Configuration Options

You can modify the application settings by editing the batch files:

- **Port**: Change `FLASK_PORT=7001` to your desired port
- **Host**: Change `FLASK_HOST=0.0.0.0` to restrict access
- **Debug**: Set `FLASK_DEBUG=True` for development mode

### Troubleshooting

#### RDKit Installation Issues
If RDKit fails to install:
```cmd
pip install rdkit-pypi
```

#### Port Already in Use
If port 7001 is busy, edit the batch files to use a different port:
```cmd
set FLASK_PORT=8080
```

#### Python Not Found
Ensure Python is installed and added to PATH:
1. Download Python from python.org
2. During installation, check "Add Python to PATH"
3. Restart Command Prompt

#### Permission Issues
Run Command Prompt as Administrator if you encounter permission errors.

### Running from PyCharm

1. Open the project folder in PyCharm
2. Configure Python interpreter to use the virtual environment (`venv\Scripts\python.exe`)
3. Install requirements via PyCharm's terminal: `pip install -r requirements.txt`
4. Run `app.py` directly from PyCharm

### Network Access

To allow access from other computers on the network:
1. Ensure `FLASK_HOST=0.0.0.0` in the batch file
2. Configure Windows Firewall to allow the port (7001)
3. Access via `http://[computer-ip]:7001`

### Production Considerations

For production deployment, consider:
- Using a proper WSGI server (Gunicorn, uWSGI)
- Setting up reverse proxy (nginx, Apache)
- Configuring SSL/HTTPS
- Setting up logging and monitoring
- Using environment variables for sensitive configuration

### File Structure After Deployment

```
ms_calculator/
├── app.py                    # Main Flask application
├── requirements.txt          # Python dependencies
├── start_app.bat            # Production startup script
├── start_app_dev.bat        # Development startup script
├── check_requirements.bat   # Setup and verification script
├── DEPLOYMENT_WINDOWS.md    # This file
├── venv/                    # Virtual environment (created by setup)
├── core/                    # Application modules
├── ui/                      # Web interface templates
└── tests/                   # Unit tests
```

### Support

If you encounter issues:
1. Check the console output for error messages
2. Verify all requirements are installed: run `check_requirements.bat`
3. Try running in development mode: `start_app_dev.bat`
4. Check the application logs in the console window
