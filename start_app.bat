@echo off
echo Starting MS Calculator Application...
echo.

REM Activate virtual environment if it exists
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo No virtual environment found. Using system Python.
)

REM Set environment variables for production
set FLASK_DEBUG=False
set FLASK_HOST=0.0.0.0
set FLASK_PORT=7001

REM Start the application
echo Starting Flask application...
python app.py

REM Keep the window open if there's an error
if errorlevel 1 (
    echo.
    echo Application encountered an error. Press any key to exit.
    pause >nul
)
