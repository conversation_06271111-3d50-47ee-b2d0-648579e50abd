@echo off
echo Starting MS Calculator Application...
echo.

REM Activate MS Calculator virtual environment if it exists
if exist "ms_calc_venv\Scripts\activate.bat" (
    echo Activating MS Calculator virtual environment...
    call ms_calc_venv\Scripts\activate.bat
) else (
    echo No MS Calculator virtual environment found. Run check_requirements.bat first.
    echo Press any key to exit.
    pause >nul
    exit /b 1
)

REM Set environment variables for production
set FLASK_DEBUG=False
set FLASK_HOST=0.0.0.0
set FLASK_PORT=8080

REM Start the application
echo Starting Flask application...
python app.py

REM Keep the window open if there's an error
if errorlevel 1 (
    echo.
    echo Application encountered an error. Press any key to exit.
    pause >nul
)
