@echo off
echo Checking Python and Dependencies...
echo.

REM Check Python version
python --version
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    pause
    exit /b 1
)

echo.
echo Checking pip...
pip --version
if errorlevel 1 (
    echo ERROR: pip is not available
    pause
    exit /b 1
)

echo.
echo Checking if virtual environment exists...
if exist "ms_calc_venv\Scripts\activate.bat" (
    echo Virtual environment found.
    call ms_calc_venv\Scripts\activate.bat
    echo Activated virtual environment.
) else (
    echo No virtual environment found. Creating isolated environment for MS Calculator...
    python -m venv ms_calc_venv
    if errorlevel 1 (
        echo ERROR: Failed to create virtual environment
        pause
        exit /b 1
    )
    call ms_calc_venv\Scripts\activate.bat
    echo Virtual environment created and activated.
)

echo.
echo Installing/updating requirements...
pip install -r requirements.txt
if errorlevel 1 (
    echo ERROR: Failed to install requirements
    pause
    exit /b 1
)

echo.
echo Running basic tests...
python -m pytest tests/ -v
if errorlevel 1 (
    echo WARNING: Some tests failed. Check the output above.
) else (
    echo All tests passed!
)

echo.
echo Setup complete! You can now run start_app.bat to start the application.
pause
