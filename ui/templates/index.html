<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MS Calculator - Mass Spectrometry Tool</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }

        .input-section {
            margin-bottom: 2rem;
        }

        .input-section h2 {
            color: #4a5568;
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2d3748;
        }

        textarea {
            width: 100%;
            padding: 1rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
            min-height: 120px;
            transition: border-color 0.3s ease;
        }

        textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            font-size: 14px;
            background-color: white;
            transition: border-color 0.3s ease;
        }

        select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .checkbox-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #667eea;
        }

        .checkbox-group label {
            margin-bottom: 0;
            font-weight: normal;
        }

        .button-group {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 140px;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        button:active {
            transform: translateY(0);
        }

        .button-secondary {
            background: linear-gradient(135deg, #4fd1c7 0%, #06b6d4 100%);
        }

        .examples {
            background: #f7fafc;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .examples h3 {
            color: #2d3748;
            margin-bottom: 0.5rem;
            font-size: 1rem;
        }

        .examples p {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #4a5568;
            margin-bottom: 0.3rem;
        }

        .results {
            margin-top: 2rem;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-top: 1rem;
        }

        .results-column {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            border-left: 4px solid;
        }

        .reactants { border-left-color: #e53e3e; }
        .reagents { border-left-color: #3182ce; }
        .products { border-left-color: #38a169; }
        .by_products { border-left-color: #9f7aea; }

        .results-column h3 {
            margin-bottom: 1rem;
            font-size: 1.2rem;
        }

        .reactants h3 { color: #e53e3e; }
        .reagents h3 { color: #3182ce; }
        .products h3 { color: #38a169; }
        .by_products h3 { color: #9f7aea; }

        .compound {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid #e2e8f0;
            transition: box-shadow 0.3s ease;
        }

        .compound:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .compound-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.8rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #edf2f7;
        }

        .smiles {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #2d3748;
            font-size: 14px;
        }

        .mass {
            font-weight: bold;
            color: #667eea;
            font-size: 16px;
        }

        .mz-values {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .mz-item {
            text-align: center;
            padding: 0.5rem;
            background: #edf2f7;
            border-radius: 6px;
        }

        .mz-mode {
            font-size: 12px;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 0.2rem;
        }

        .mz-value {
            font-size: 14px;
            font-weight: bold;
            color: #2d3748;
        }

        .isotope-pattern {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 6px;
            padding: 0.8rem;
            margin-top: 0.8rem;
        }

        .isotope-pattern h4 {
            color: #0369a1;
            font-size: 13px;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .isotope-peaks {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            color: #1e40af;
            line-height: 1.4;
        }

        .adducts {
            background: #fefce8;
            border: 1px solid #facc15;
            border-radius: 6px;
            padding: 0.8rem;
            margin-top: 0.8rem;
        }

        .adducts h4 {
            color: #a16207;
            font-size: 13px;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .adduct-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 0.3rem;
        }

        .adduct-item {
            display: flex;
            justify-content: space-between;
            font-size: 11px;
            padding: 0.2rem 0;
        }

        .adduct-name {
            font-weight: 600;
            color: #92400e;
        }

        .adduct-mz {
            color: #451a03;
        }

        .invalid-compounds {
            background: #fef2f2;
            border: 1px solid #fca5a5;
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
        }

        .invalid-compounds h4 {
            color: #dc2626;
            margin-bottom: 0.5rem;
        }

        .invalid-item {
            font-family: 'Courier New', monospace;
            color: #7f1d1d;
            font-size: 14px;
            margin-bottom: 0.3rem;
        }

        .halogen-note {
            background: #fdf4ff;
            border: 1px solid #d8b4fe;
            border-radius: 6px;
            padding: 0.5rem;
            margin-top: 0.5rem;
            font-size: 12px;
            color: #7c3aed;
        }

        .flash-messages {
            margin-bottom: 1rem;
        }

        .flash-message {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 0.5rem;
        }

        .flash-error {
            background: #fed7d7;
            color: #c53030;
            border: 1px solid #feb2b2;
        }

        .flash-success {
            background: #c6f6d5;
            color: #2f855a;
            border: 1px solid #9ae6b4;
        }

        .footer {
            text-align: center;
            margin-top: 3rem;
            padding: 1rem;
            color: white;
            opacity: 0.8;
        }

        .footer a {
            color: #bae6fd;
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        .by-product-score {
            background: #f3e8ff;
            border: 1px solid #c084fc;
            border-radius: 6px;
            padding: 0.5rem;
            margin-top: 0.5rem;
            font-size: 12px;
            color: #7c3aed;
        }

        .by-product-score strong {
            color: #5b21b6;
        }

        .carbonylation-mechanism {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 6px;
            padding: 0.5rem;
            margin-top: 0.5rem;
            font-size: 12px;
            color: #0369a1;
        }

        .carbonylation-mechanism strong {
            color: #075985;
        }

        .by-product-item {
            background: #faf5ff;
            border: 1px solid #e9d5ff;
            border-radius: 8px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .by-products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 1rem;
            margin-top: 0.5rem;
        }

        .by-product-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.8rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #e9d5ff;
        }

        .by-product-rank {
            background: #9f7aea;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .card {
                padding: 1.5rem;
            }
            
            .results-grid {
                grid-template-columns: 1fr;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            button {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>MS Calculator</h1>
            <p>Mass Spectrometry Tool for SMILES Analysis</p>
        </div>

        <div class="card">
            <div class="input-section">
                
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <div class="flash-messages">
                            {% for category, message in messages %}
                                <div class="flash-message flash-{{ category }}">{{ message }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endwith %}

                <form method="POST" action="/calculate">
                    <div class="form-group">
                        <label for="smiles_input">Enter SMILES (single line):</label>
                        <textarea 
                            id="smiles_input" 
                            name="smiles_input" 
                            placeholder="Enter SMILES strings here, e.g.:&#10;CCO&#10;CCO>>CC(=O)O&#10;C1=CC=CC=C1"
                            required>{{ input_smiles or request.form.smiles_input or '' }}</textarea>
                    </div>

                    <div class="checkbox-group">
                        <input type="checkbox" id="show_adducts" name="show_adducts" 
                               {% if show_adducts or request.form.show_adducts %}checked{% endif %}>
                        <label for="show_adducts">Show common ESI adducts ([M+Na]+, [M+K]+, [M+Cl]-, etc.)</label>
                    </div>

                    <div class="form-group">
                        <label for="reaction_type">Reaction Type:</label>
                        <select id="reaction_type" name="reaction_type">
                            <option value="none" {% if (reaction_type == 'none') or (not reaction_type and not request.form.reaction_type) or (request.form.reaction_type == 'none') %}selected{% endif %}>No by-products prediction</option>
                            <option value="插羰反应" {% if (reaction_type == '插羰反应') or (request.form.reaction_type == '插羰反应') %}selected{% endif %}>插羰反应 (Carbonylation)</option>
                            <option value="其他反应" {% if (reaction_type == '其他反应') or (request.form.reaction_type == '其他反应') %}selected{% endif %}>其他反应 (Other reactions - FMT model)</option>
                        </select>
                    </div>

                    <div class="button-group">
                        <button type="submit">Calculate</button>
                        <button type="button" class="button-secondary" onclick="window.location.href='/adducts'">View Adducts Reference</button>
                    </div>
                </form>

                
            </div>
        </div>

        {% if results %}
        <div class="card">
            <div class="results">
                
                <div class="results-grid">
                    {% for category, compounds in results.items() %}
                        {% if compounds and (category == 'reactants' or category == 'reagents' or category == 'products') %}
                        <div class="results-column {{ category }}">
                            <h3>{{ category.title() }} ({{ compounds|length }})</h3>
                            
                            {% for smiles, result in compounds.items() %}
                                {% if result.mass %}
                                <div class="compound">
                                    <div class="compound-header">
                                        <span class="smiles">{{ smiles }}</span>
                                        <span class="mass">{{ result.mass }} Da</span>
                                    </div>
                                    
                                    <div class="mz-values">
                                        <div class="mz-item">
                                            <div class="mz-mode">ESI+</div>
                                            <div class="mz-value">{{ result.mz_ESI_pos or 'N/A' }}</div>
                                        </div>
                                        <div class="mz-item">
                                            <div class="mz-mode">ESI-</div>
                                            <div class="mz-value">{{ result.mz_ESI_neg or 'N/A' }}</div>
                                        </div>
                                        <div class="mz-item">
                                            <div class="mz-mode">EI</div>
                                            <div class="mz-value">{{ result.mz_EI or 'N/A' }}</div>
                                        </div>
                                    </div>

                                    {% if result.svg %}
                                    <div class="compound-structure" style="margin-top: 0.5rem;">
                                        {{ result.svg | safe }}
                                    </div>
                                    {% endif %}

                                    {% if result.isotope_pattern %}
                                    <div class="isotope-pattern">
                                        <h4>Isotope Pattern [M+H]+:</h4>
                                        <div class="isotope-peaks">{{ result.isotope_pattern_str }}</div>
                                    </div>
                                    {% endif %}

                                    {% if request.form.show_adducts and result.adducts %}
                                    <div class="adducts">
                                        <h4>Common ESI Adducts:</h4>
                                        <div class="adduct-grid">
                                            {% for adduct, mz in result.adducts.items() %}
                                                {% if mz %}
                                                <div class="adduct-item">
                                                    <span class="adduct-name">{{ adduct }}</span>
                                                    <span class="adduct-mz">{{ mz }}</span>
                                                </div>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                    </div>
                                    {% endif %}

                                    {% if result.has_chlorine or result.has_bromine %}
                                    <div class="halogen-note">
                                        <strong>Note:</strong>
                                        {% if result.has_chlorine %}
                                            Contains Cl - expect M/M+2 isotope pattern (~3:1 ratio).
                                        {% endif %}
                                        {% if result.has_bromine %}
                                            Contains Br - expect M/M+2 isotope pattern (~1:1 ratio).
                                        {% endif %}
                                    </div>
                                    {% endif %}
                                </div>
                                {% endif %}
                            {% endfor %}
                        </div>
                        {% endif %}
                    {% endfor %}
                </div>

                <!-- Show by-products if predicted -->
                {% if results.by_products %}
                <div class="card" style="margin-top: 2rem;">
                    {% if results.by_products_type == 'carbonylation' %}
                    <h2>Carbonylation By-Products</h2>
                    <p style="color: #666; margin-bottom: 1.5rem;">By-products predicted for carbonylation reactions, categorized by formation mechanism</p>
                    {% else %}
                    <h2>Predicted By-Products</h2>
                    <p style="color: #666; margin-bottom: 1.5rem;">via DAVINCIRS FMT model, ranked by confidence score</p>
                    {% endif %}
                    
                    {% for input_smiles, by_products_list in results.by_products.items() %}
                        {% if by_products_list %}
                        <div style="margin-bottom: 2rem;">
                            <h3 style="color: #9f7aea; margin-bottom: 1rem;">From: <span style="font-family: 'Courier New', monospace;">{{ input_smiles }}</span></h3>
                            
                            <div class="by-products-grid">
                            {% for by_product in by_products_list %}
                            <div class="by-product-item">
                                <div class="by-product-header">
                                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                                        {% if results.by_products_type == 'carbonylation' %}
                                        <div class="by-product-rank">{{ loop.index }}</div>
                                        <span class="smiles">{{ by_product.smiles }}</span>
                                        {% else %}
                                        <div class="by-product-rank">{{ loop.index }}</div>
                                        <span class="smiles">{{ by_product.smiles }}</span>
                                        {% endif %}
                                    </div>
                                    {% if by_product.molecular_weight %}
                                    <span class="mass">{{ by_product.molecular_weight }} Da</span>
                                    {% endif %}
                                </div>

                                {% if by_product.svg %}
                                <div class="compound-structure" style="margin-bottom: 0.5rem;">
                                    {{ by_product.svg | safe }}
                                </div>
                                {% endif %}
                                
                                {% if results.by_products_type == 'carbonylation' %}
                                <div class="carbonylation-mechanism">
                                    <strong>Formation Mechanism:</strong> {{ by_product.role }}
                                </div>
                                {% else %}
                                <div class="by-product-score">
                                    <strong>Prediction Score:</strong> {{ by_product.score }}
                                </div>
                                {% endif %}
                                
                                {% if by_product.molecular_weight %}
                                <div class="mz-values">
                                    <div class="mz-item">
                                        <div class="mz-mode">ESI+</div>
                                        <div class="mz-value">{{ by_product.mz_ESI_pos or 'N/A' }}</div>
                                    </div>
                                    <div class="mz-item">
                                        <div class="mz-mode">ESI-</div>
                                        <div class="mz-value">{{ by_product.mz_ESI_neg or 'N/A' }}</div>
                                    </div>
                                    <div class="mz-item">
                                        <div class="mz-mode">EI</div>
                                        <div class="mz-value">{{ by_product.mz_EI or 'N/A' }}</div>
                                    </div>
                                </div>
                                
                                {% if show_adducts and by_product.adducts %}
                                <div class="adducts">
                                    <h4>Common ESI Adducts:</h4>
                                    <div class="adduct-grid">
                                        {% for adduct, mz in by_product.adducts.items() %}
                                            {% if mz %}
                                            <div class="adduct-item">
                                                <span class="adduct-name">{{ adduct }}</span>
                                                <span class="adduct-mz">{{ mz }}</span>
                                            </div>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                </div>
                                {% endif %}

                                {% if by_product.has_chlorine or by_product.has_bromine %}
                                <div class="halogen-note">
                                    <strong>Note:</strong>
                                    {% if by_product.has_chlorine %}
                                        Contains Cl - expect M/M+2 isotope pattern (~3:1 ratio).
                                    {% endif %}
                                    {% if by_product.has_bromine %}
                                        Contains Br - expect M/M+2 isotope pattern (~1:1 ratio).
                                    {% endif %}
                                </div>
                                {% endif %}
                                {% endif %}
                            </div>
                            {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Show invalid SMILES if any -->
                {% if results.invalid %}
                <div class="invalid-compounds">
                    <h4>Invalid SMILES:</h4>
                    {% for smiles in results.invalid %}
                        <div class="invalid-item">{{ smiles }}</div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}

        <div class="footer">
            <p>Built with RDKit • <a href="/adducts">ESI Adducts Reference</a> • <a href="https://github.com">View Source</a></p>
        </div>
    </div>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-expand textarea based on content
            const textarea = document.getElementById('smiles_input');
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = this.scrollHeight + 'px';
            });

            // Add hover effects to compounds
            const compounds = document.querySelectorAll('.compound');
            compounds.forEach(compound => {
                compound.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                    this.style.transition = 'transform 0.3s ease';
                });
                
                compound.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html> 