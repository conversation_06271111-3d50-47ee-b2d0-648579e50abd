<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESI Adducts Reference - MS Calculator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            color: white;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .nav-links {
            text-align: center;
            margin-bottom: 2rem;
        }

        .nav-links a {
            color: white;
            text-decoration: none;
            margin: 0 1rem;
            padding: 0.5rem 1rem;
            border: 1px solid rgba(255,255,255,0.3);
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
            transform: translateY(-2px);
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }

        .intro {
            text-align: center;
            margin-bottom: 2rem;
            color: #4a5568;
        }

        .intro h2 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
            color: #2d3748;
        }

        .modes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .mode-section {
            background: #f8fafc;
            border-radius: 12px;
            padding: 1.5rem;
            border-left: 4px solid;
        }

        .positive-mode {
            border-left-color: #e53e3e;
        }

        .negative-mode {
            border-left-color: #3182ce;
        }

        .mode-section h3 {
            font-size: 1.4rem;
            margin-bottom: 1rem;
            color: #2d3748;
        }

        .positive-mode h3 {
            color: #e53e3e;
        }

        .negative-mode h3 {
            color: #3182ce;
        }

        .adducts-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1rem;
        }

        .adducts-table th,
        .adducts-table td {
            text-align: left;
            padding: 0.8rem;
            border-bottom: 1px solid #e2e8f0;
        }

        .adducts-table th {
            background: #edf2f7;
            font-weight: 600;
            color: #4a5568;
        }

        .adduct-name {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #2d3748;
        }

        .mass-change {
            font-family: 'Courier New', monospace;
            color: #667eea;
        }

        .description {
            color: #4a5568;
            font-size: 0.9rem;
        }

        .notes {
            background: #fef5e7;
            border: 1px solid #f6e05e;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .notes h3 {
            color: #975a16;
            margin-bottom: 1rem;
        }

        .notes ul {
            margin-left: 1.5rem;
            color: #744210;
        }

        .notes li {
            margin-bottom: 0.5rem;
        }

        .example-section {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
        }

        .example-section h3 {
            color: #0369a1;
            margin-bottom: 1rem;
        }

        .example-calculation {
            background: white;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }

        .formula {
            color: #1e40af;
            font-weight: bold;
        }

        .footer {
            text-align: center;
            margin-top: 3rem;
            padding: 1rem;
            color: white;
            opacity: 0.8;
        }

        .footer a {
            color: #bae6fd;
            text-decoration: none;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .card {
                padding: 1.5rem;
            }
            
            .modes-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>ESI Adducts Reference</h1>
            <p>Common adduct ions in electrospray ionization mass spectrometry</p>
        </div>

        <div class="nav-links">
            <a href="/">← Back to Calculator</a>
            <a href="/api/adducts" target="_blank">JSON API</a>
        </div>

        <div class="card">
            <div class="intro">
                <h2>Common ESI Adduct Ions</h2>
                <p>In electrospray ionization (ESI), molecules can form various adduct ions depending on the mobile phase composition and ionization conditions. Below are the most commonly observed adducts.</p>
            </div>

            <div class="modes-grid">
                <!-- Positive Mode Adducts -->
                <div class="mode-section positive-mode">
                    <h3>ESI+ Mode Adducts</h3>
                    <table class="adducts-table">
                        <thead>
                            <tr>
                                <th>Adduct Ion</th>
                                <th>Mass Change (Da)</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for adduct, info in adduct_data.positive.items() %}
                            <tr>
                                <td class="adduct-name">{{ adduct }}</td>
                                <td class="mass-change">+{{ "%.6f"|format(info.mass_change) }}</td>
                                <td class="description">{{ info.description }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Negative Mode Adducts -->
                <div class="mode-section negative-mode">
                    <h3>ESI- Mode Adducts</h3>
                    <table class="adducts-table">
                        <thead>
                            <tr>
                                <th>Adduct Ion</th>
                                <th>Mass Change (Da)</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for adduct, info in adduct_data.negative.items() %}
                            <tr>
                                <td class="adduct-name">{{ adduct }}</td>
                                <td class="mass-change">{{ "%.6f"|format(info.mass_change) }}</td>
                                <td class="description">{{ info.description }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="example-section">
                <h3>Example Calculations</h3>
                <div class="example-calculation">
                    <strong>Caffeine (C₈H₁₀N₄O₂, MW = 194.08)</strong><br>
                    <span class="formula">[M+H]⁺</span> = 194.08 + 1.007276 = <strong>195.09 m/z</strong><br>
                    <span class="formula">[M+Na]⁺</span> = 194.08 + 22.989218 = <strong>217.07 m/z</strong><br>
                    <span class="formula">[M+K]⁺</span> = 194.08 + 38.963158 = <strong>233.04 m/z</strong>
                </div>
                <div class="example-calculation">
                    <strong>Aspirin (C₉H₈O₄, MW = 180.04)</strong><br>
                    <span class="formula">[M-H]⁻</span> = 180.04 - 1.007276 = <strong>179.03 m/z</strong><br>
                    <span class="formula">[M+Cl]⁻</span> = 180.04 + 34.969402 = <strong>215.01 m/z</strong><br>
                    <span class="formula">[M+HCOO]⁻</span> = 180.04 + 44.998201 = <strong>225.04 m/z</strong>
                </div>
            </div>

            <div class="notes">
                <h3>Important Notes</h3>
                <ul>
                    <li><strong>Mobile Phase Effects:</strong> Buffer composition and pH significantly influence adduct formation</li>
                    <li><strong>Concentration Dependent:</strong> Higher salt concentrations increase Na⁺/K⁺ adduct formation</li>
                    <li><strong>Ionization Efficiency:</strong> [M+H]⁺ and [M-H]⁻ typically show highest sensitivity</li>
                    <li><strong>Isotope Patterns:</strong> Chloride and bromide adducts show characteristic isotope patterns</li>
                    <li><strong>Matrix Effects:</strong> Biological samples may show additional matrix-specific adducts</li>
                    <li><strong>Instrumental Settings:</strong> Source temperature and voltages affect adduct distribution</li>
                </ul>
            </div>
        </div>

        <div class="footer">
            <p>Built with RDKit • <a href="/">MS Calculator</a> • <a href="https://github.com">View Source</a></p>
        </div>
    </div>
</body>
</html> 