# MS Calculator

A modular web application for calculating molecular masses and predicting mass spectrometry peaks from SMILES reactions.

## Features

- **SMILES Parsing**: Supports single molecules, mixtures, and chemical reactions
- **Mass Calculation**: Computes monoisotopic molecular mass (1 decimal place precision)
- **MS Peak Prediction**: Predicts m/z values for ESI+, ESI-, and EI ionization modes
- **Reaction Analysis**: Parses reactions into reactants, reagents, and products
- **Error Handling**: Identifies and reports invalid SMILES separately
- **Responsive Web Interface**: Clean, mobile-friendly single-page application
- **API Access**: RESTful API for programmatic access

## Supported Input Formats

- **Single molecules**: `CCO` (ethanol)
- **Mixtures**: `CCO.CC(=O)O` (ethanol + acetic acid)
- **Simple reactions**: `CCO>>CC(=O)O` (ethanol → acetic acid)
- **Reactions with reagents**: `CCO>O>CC(=O)O` (ethanol + oxygen → acetic acid)

## Installation

1. **Clone or download the project**:
   ```bash
   cd ms_calculator
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python app.py
   ```

4. **Access the web interface**:
   Open your browser and navigate to `http://localhost:5000`

## Project Structure

```
ms_calculator/
├── app.py                # Flask application entry point
├── requirements.txt      # Python dependencies
├── README.md            # This file
├── core/                # Core calculation modules
│   ├── __init__.py
│   ├── validator.py     # SMILES validation
│   ├── parser.py        # Reaction parsing
│   └── calculator.py    # Mass and m/z calculations
├── ui/                  # User interface
│   └── templates/
│       └── index.html   # Main web interface
└── tests/               # Unit tests
    ├── __init__.py
    ├── test_validator.py
    ├── test_parser.py
    └── test_calculator.py
```

## API Usage

### Web Interface
- **GET** `/` - Main interface
- **POST** `/calculate` - Process SMILES and return results

### REST API
- **POST** `/api/calculate` - JSON API endpoint

**Example API request**:
```bash
curl -X POST http://localhost:5000/api/calculate \
  -H "Content-Type: application/json" \
  -d '{"smiles": "CCO>>CC(=O)O"}'
```

**Example API response**:
```json
{
  "reactants": [
    {
      "smiles": "CCO",
      "mass": 46.0,
      "mz_values": {
        "ESI+": 47.0,
        "ESI-": 45.0,
        "EI": 46.0
      }
    }
  ],
  "reagents": [],
  "products": [
    {
      "smiles": "CC(=O)O",
      "mass": 60.0,
      "mz_values": {
        "ESI+": 61.0,
        "ESI-": 59.0,
        "EI": 60.0
      }
    }
  ],
  "invalid_smiles": [],
  "original_input": "CCO>>CC(=O)O"
}
```

## Ionization Modes

- **ESI+**: Electrospray ionization positive mode - adds proton [M+H]+
- **ESI-**: Electrospray ionization negative mode - loses proton [M-H]-
- **EI**: Electron ionization - molecular ion [M]•+

## Running Tests

Run all unit tests:
```bash
cd ms_calculator
python -m pytest tests/ -v
```

Run specific test modules:
```bash
python -m pytest tests/test_validator.py -v
python -m pytest tests/test_parser.py -v
python -m pytest tests/test_calculator.py -v
```

## Dependencies

- **Flask**: Web framework
- **RDKit**: Chemical informatics library for SMILES processing
- **Pytest**: Testing framework

## Error Handling

The application gracefully handles:
- Invalid SMILES strings
- Malformed reaction formats
- Network errors
- RDKit processing errors

Invalid SMILES are collected and displayed separately, while valid molecules are processed normally.

## Future Enhancements

- Impurity prediction based on reaction types
- Mass database integration for compound identification
- Additional ionization modes (APCI, MALDI, etc.)
- Batch file processing
- Export functionality (CSV, PDF)

## Technical Notes

- Monoisotopic masses are calculated using RDKit's `ExactMolWt` descriptor
- All mass values are rounded to 1 decimal place as specified
- Proton mass constant: 1.007276 Da
- The application uses modular design for easy extension and testing

## License

This project is developed for educational and research purposes. 