"""
Mass and m/z calculation module for MS calculator.
Calculates monoisotopic mass and predicts m/z values for different ionization modes.
Enhanced with common ESI adduct ions and isotope pattern calculations.
"""

from typing import Dict, Optional, List, Tuple
from rdkit import Chem
from rdkit.Chem import Descriptors, rdMolDescriptors, GetPeriodicTable
from rdkit.Chem import rdDepictor
from rdkit.Chem.Draw import rdMolDraw2D
import logging
import re
import requests
import json
from collections import defaultdict
from itertools import product

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get periodic table instance
pt = GetPeriodicTable()

# Accurate masses for common adducts (monoisotopic masses)
# Based on reference data from Waters, UC Davis, and UCR mass spectrometry facilities
ADDUCT_MASSES = {
    # Positive ion mode adducts
    'H+': 1.007276,      # Proton
    'Na+': 22.989218,    # Sodium
    'K+': 38.963158,     # Potassium 
    'NH4+': 18.033823,   # Ammonium
    
    # Negative ion mode adducts
    'H-': -1.007276,     # Deprotonation
    'Cl-': 34.969402,    # Chloride
    'Br-': 78.918336,    # Bromide
    'HCOO-': 44.998201,  # Formate
    'CH3COO-': 59.013851, # Acetate
    
    # Neutral losses (electron removal/addition)
    'e-': -0.000549,     # Electron removal (EI+)
}

# Isotope data for major elements (atomic number -> isotope masses and abundances)
ISOTOPE_DATA = {
    1: [(1.00782503223, 99.9885), (2.01410177812, 0.0115)],  # H
    6: [(12.0, 98.93), (13.0033548378, 1.07)],  # C
    7: [(14.0030740048, 99.636), (15.0001088982, 0.364)],  # N
    8: [(15.9949146223, 99.757), (16.9991317012, 0.038), (17.9991610044, 0.205)],  # O
    9: [(18.9984032, 100.0)],  # F
    15: [(30.9737619984, 100.0)],  # P
    16: [(31.9720718, 94.99), (32.9714589, 0.75), (33.9678668, 4.25), (35.9670808, 0.01)],  # S
    17: [(34.9688527, 75.78), (36.9659026, 24.22)],  # Cl
    35: [(78.9183376, 50.69), (80.9162897, 49.31)],  # Br
    53: [(126.9044719, 100.0)],  # I
}

def parse_molecular_formula(formula: str) -> Dict[str, int]:
    """
    Parse molecular formula string into element counts.
    
    Args:
        formula: Molecular formula string (e.g., "C2H5Cl")
        
    Returns:
        Dict mapping element symbols to counts
    """
    # Regex to match element symbol followed by optional count
    pattern = r'([A-Z][a-z]?)(\d*)'
    matches = re.findall(pattern, formula)
    
    element_counts = {}
    for element, count_str in matches:
        count = int(count_str) if count_str else 1
        element_counts[element] = element_counts.get(element, 0) + count
    
    return element_counts

def calculate_isotope_pattern(smiles: str, max_peaks: int = 10, min_abundance: float = 0.1) -> List[Tuple[float, float]]:
    """
    Calculate isotope pattern for a molecule.
    
    Args:
        smiles: SMILES string
        max_peaks: Maximum number of peaks to return
        min_abundance: Minimum relative abundance threshold (%)
        
    Returns:
        List of (m/z, relative_abundance) tuples, sorted by abundance (descending)
    """
    try:
        mol = Chem.MolFromSmiles(smiles)
        if not mol:
            return []
        
        # Get molecular formula
        formula = rdMolDescriptors.CalcMolFormula(mol)
        element_counts = parse_molecular_formula(formula)
        
        # Calculate all possible isotope combinations
        isotope_combinations = []
        
        # Get atomic numbers for elements
        atomic_numbers = {}
        for element in element_counts:
            atomic_numbers[element] = pt.GetAtomicNumber(element)
        
        # Generate all isotope combinations
        element_isotopes = {}
        for element, count in element_counts.items():
            atomic_num = atomic_numbers[element]
            if atomic_num in ISOTOPE_DATA:
                isotopes = ISOTOPE_DATA[atomic_num]
                element_isotopes[element] = [(mass, abundance/100, count) for mass, abundance in isotopes]
            else:
                # Use most common isotope if not in our data
                mass = pt.GetMostCommonIsotopeMass(atomic_num)
                element_isotopes[element] = [(mass, 1.0, count)]
        
        # Generate all combinations
        combinations = []
        elements = list(element_isotopes.keys())
        
        def generate_combinations(elem_idx, current_mass, current_abundance):
            if elem_idx == len(elements):
                combinations.append((current_mass, current_abundance))
                return
            
            element = elements[elem_idx]
            count = element_counts[element]
            isotopes = element_isotopes[element]
            
            if len(isotopes) == 1:
                # Only one isotope available
                mass1, abundance1, _ = isotopes[0]
                new_mass = current_mass + mass1 * count
                new_abundance = current_abundance * (abundance1 ** count)
                generate_combinations(elem_idx + 1, new_mass, new_abundance)
            
            elif len(isotopes) >= 2:
                # Multiple isotopes available - generate combinations for different numbers of each isotope
                mass1, abundance1, _ = isotopes[0]
                mass2, abundance2, _ = isotopes[1]
                
                from math import factorial
                for num_isotope1 in range(count + 1):
                    num_isotope2 = count - num_isotope1
                    
                    # Calculate multinomial coefficient
                    if count > 0:
                        coeff = factorial(count) // (factorial(num_isotope1) * factorial(num_isotope2))
                        new_mass = current_mass + mass1 * num_isotope1 + mass2 * num_isotope2
                        new_abundance = current_abundance * coeff * (abundance1 ** num_isotope1) * (abundance2 ** num_isotope2)
                        generate_combinations(elem_idx + 1, new_mass, new_abundance)
        
        generate_combinations(0, 0.0, 1.0)
        
        # Group by mass (round to avoid floating point issues) and sum abundances
        mass_groups = defaultdict(float)
        for mass, abundance in combinations:
            rounded_mass = round(mass, 2)
            mass_groups[rounded_mass] += abundance
        
        # Convert to list and sort by abundance
        isotope_peaks = [(mass, abundance * 100) for mass, abundance in mass_groups.items()]
        isotope_peaks.sort(key=lambda x: x[1], reverse=True)
        
        # Normalize to most abundant peak = 100%
        if isotope_peaks:
            max_abundance = isotope_peaks[0][1]
            isotope_peaks = [(mass, (abundance / max_abundance) * 100) for mass, abundance in isotope_peaks]
        
        # Filter by minimum abundance and limit number of peaks
        filtered_peaks = [(mass, abundance) for mass, abundance in isotope_peaks 
                         if abundance >= min_abundance][:max_peaks]
        
        return filtered_peaks
        
    except Exception as e:
        logger.error(f"Error calculating isotope pattern for {smiles}: {e}")
        return []

def format_isotope_pattern(pattern: List[Tuple[float, float]]) -> str:
    """
    Format isotope pattern for display.
    
    Args:
        pattern: List of (m/z, abundance) tuples
        
    Returns:
        Formatted string representation
    """
    if not pattern:
        return "No isotope pattern calculated"
    
    formatted_peaks = []
    for mass, abundance in pattern:
        formatted_peaks.append(f"{mass:.2f} ({abundance:.1f}%)")
    
    return ", ".join(formatted_peaks)

def smiles_to_svg(smiles: str, width: int = 250, height: int = 180) -> str:
    """
    Generate a 2D SVG depiction for a SMILES string using RDKit.

    Args:
        smiles: SMILES string
        width: SVG width in pixels
        height: SVG height in pixels

    Returns:
        SVG markup as a string, or empty string on failure.
    """
    try:
        if not smiles or not smiles.strip():
            return ""
        mol = Chem.MolFromSmiles(smiles.strip())
        if mol is None:
            return ""

        rdDepictor.Compute2DCoords(mol)
        drawer = rdMolDraw2D.MolDraw2DSVG(width, height)
        rdMolDraw2D.PrepareAndDrawMolecule(drawer, mol)
        drawer.FinishDrawing()
        svg = drawer.GetDrawingText()
        return svg
    except Exception as e:
        logger.error(f"Error generating SVG for SMILES '{smiles}': {e}")
        return ""

def calculate_mass(smiles: str) -> Optional[float]:
    """
    Calculate monoisotopic mass of a molecule from SMILES.
    
    Args:
        smiles: SMILES string representation of molecule
        
    Returns:
        Monoisotopic mass in Da, rounded to 1 decimal place, or None if invalid
    """
    if not smiles or not smiles.strip():
        return None
        
    try:
        mol = Chem.MolFromSmiles(smiles.strip())
        if mol is None:
            return None
        
        mass = Descriptors.ExactMolWt(mol)
        return round(mass, 1)
    
    except Exception as e:
        logger.error(f"Error calculating mass for SMILES '{smiles}': {e}")
        return None

def calculate_mz(mass: float, ionization_mode: str) -> Optional[float]:
    """
    Calculate m/z value for given mass and ionization mode.
    
    Args:
        mass: Molecular mass in Da
        ionization_mode: ESI+, ESI-, or EI
        
    Returns:
        m/z value rounded to 1 decimal place, or None if invalid
    """
    if mass is None or mass <= 0:
        return None
    
    try:
        if ionization_mode == "ESI+":
            # [M+H]+ ion
            mz = mass + ADDUCT_MASSES['H+']
        elif ionization_mode == "ESI-":
            # [M-H]- ion  
            mz = mass + ADDUCT_MASSES['H-']
        elif ionization_mode == "EI":
            # Molecular ion [M]+•
            mz = mass + ADDUCT_MASSES['e-']
        else:
            logger.warning(f"Unknown ionization mode: {ionization_mode}")
            return None
            
        return round(mz, 1)
    
    except Exception as e:
        logger.error(f"Error calculating m/z for mass {mass} in mode {ionization_mode}: {e}")
        return None

def calculate_adduct_mz(mass: float, adduct: str) -> Optional[float]:
    """
    Calculate m/z for specific adduct.
    
    Args:
        mass: Molecular mass in Da
        adduct: Adduct type (e.g., 'Na+', 'K+', 'Cl-')
        
    Returns:
        m/z value or None if invalid
    """
    if mass is None or mass <= 0:
        return None
        
    if adduct not in ADDUCT_MASSES:
        return None
        
    try:
        if adduct in ['H-', 'e-']:
            # Negative adducts (subtract mass)
            mz = mass + ADDUCT_MASSES[adduct]
        else:
            # Positive adducts (add mass)  
            mz = mass + ADDUCT_MASSES[adduct]
        
        return round(mz, 1)
    
    except Exception as e:
        logger.error(f"Error calculating adduct m/z for {adduct}: {e}")
        return None

def calc_all_adducts(smiles: str, mode: str = 'both') -> Dict[str, Optional[float]]:
    """
    Calculate m/z for all relevant adducts.
    
    Args:
        smiles: SMILES string
        mode: 'positive', 'negative', or 'both'
        
    Returns:
        Dict of adduct -> m/z values
    """
    mass = calculate_mass(smiles)
    if mass is None:
        return {}
    
    results = {}
    
    if mode in ['positive', 'both']:
        # Positive mode adducts
        for adduct in ['H+', 'Na+', 'K+', 'NH4+']:
            results[f"[M+{adduct[:-1]}]+"] = calculate_adduct_mz(mass, adduct)
    
    if mode in ['negative', 'both']:
        # Negative mode adducts  
        results["[M-H]-"] = calculate_adduct_mz(mass, 'H-')
        for adduct in ['Cl-', 'Br-', 'HCOO-', 'CH3COO-']:
            if adduct == 'Cl-':
                results["[M+Cl]-"] = calculate_adduct_mz(mass, adduct)
            elif adduct == 'Br-':
                results["[M+Br]-"] = calculate_adduct_mz(mass, adduct)
            elif adduct == 'HCOO-':
                results["[M+HCOO]-"] = calculate_adduct_mz(mass, adduct)
            elif adduct == 'CH3COO-':
                results["[M+CH3COO]-"] = calculate_adduct_mz(mass, adduct)
    
    return results

def has_halogen(smiles: str) -> Tuple[bool, bool]:
    """
    Check if molecule contains halogens (Cl, Br).
    
    Args:
        smiles: SMILES string
        
    Returns:
        Tuple of (has_chlorine, has_bromine)
    """
    try:
        mol = Chem.MolFromSmiles(smiles)
        if mol is None:
            return False, False
        
        formula = rdMolDescriptors.CalcMolFormula(mol)
        has_cl = 'Cl' in formula
        has_br = 'Br' in formula
        
        return has_cl, has_br
    
    except Exception as e:
        logger.error(f"Error checking halogens in {smiles}: {e}")
        return False, False

def calculate_data(smiles_list: List[str]) -> Dict[str, Dict]:
    """
    Calculate mass and m/z data for a list of SMILES.
    
    Args:
        smiles_list: List of SMILES strings
        
    Returns:
        Dict mapping SMILES to calculation results
    """
    results = {}
    
    for smiles in smiles_list:
        if not smiles or not smiles.strip():
            continue
            
        smiles = smiles.strip()
        mass = calculate_mass(smiles)
        
        if mass is not None:
            # Calculate isotope pattern for [M+H]+ 
            isotope_pattern = calculate_isotope_pattern(smiles)
            
            results[smiles] = {
                'mass': mass,
                'mz_ESI_pos': calculate_mz(mass, "ESI+"),
                'mz_ESI_neg': calculate_mz(mass, "ESI-"), 
                'mz_EI': calculate_mz(mass, "EI"),
                'has_chlorine': has_halogen(smiles)[0],
                'has_bromine': has_halogen(smiles)[1],
                'isotope_pattern': isotope_pattern,
                'isotope_pattern_str': format_isotope_pattern(isotope_pattern),
                'adducts': calc_all_adducts(smiles),
                'svg': smiles_to_svg(smiles)
            }
        else:
            results[smiles] = {
                'mass': None,
                'mz_ESI_pos': None,
                'mz_ESI_neg': None,
                'mz_EI': None,
                'has_chlorine': False,
                'has_bromine': False,
                'isotope_pattern': [],
                'isotope_pattern_str': "Invalid SMILES",
                'adducts': {},
                'svg': ""
            }
    
    return results

def calculate_batch_data(reactants: List[str], reagents: List[str], products: List[str]) -> Dict[str, Dict]:
    """
    Calculate data for reactants, reagents, and products.
    
    Args:
        reactants: List of reactant SMILES
        reagents: List of reagent SMILES  
        products: List of product SMILES
        
    Returns:
        Dict with 'reactants', 'reagents', 'products' keys containing calculation results
    """
    return {
        'reactants': calculate_data(reactants),
        'reagents': calculate_data(reagents),
        'products': calculate_data(products)
    }

def get_adduct_info() -> Dict[str, Dict]:
    """
    Get information about available adducts.
    
    Returns:
        Dict containing adduct information organized by mode
    """
    return {
        'positive': {
            '[M+H]+': {'mass_change': ADDUCT_MASSES['H+'], 'description': 'Protonated molecular ion'},
            '[M+Na]+': {'mass_change': ADDUCT_MASSES['Na+'], 'description': 'Sodium adduct'},
            '[M+K]+': {'mass_change': ADDUCT_MASSES['K+'], 'description': 'Potassium adduct'},
            '[M+NH4]+': {'mass_change': ADDUCT_MASSES['NH4+'], 'description': 'Ammonium adduct'}
        },
        'negative': {
            '[M-H]-': {'mass_change': ADDUCT_MASSES['H-'], 'description': 'Deprotonated molecular ion'},
            '[M+Cl]-': {'mass_change': ADDUCT_MASSES['Cl-'], 'description': 'Chloride adduct'},
            '[M+Br]-': {'mass_change': ADDUCT_MASSES['Br-'], 'description': 'Bromide adduct'},
            '[M+HCOO]-': {'mass_change': ADDUCT_MASSES['HCOO-'], 'description': 'Formate adduct'},
            '[M+CH3COO]-': {'mass_change': ADDUCT_MASSES['CH3COO-'], 'description': 'Acetate adduct'}
        }
    }

def predict_by_products(smiles: str, top_k: int = 10) -> List[Dict]:
    """
    Predict by-products for a given SMILES using the FMT inference API.
    
    Args:
        smiles: SMILES string of the input molecule
        top_k: Number of top predictions to return
        
    Returns:
        List of dictionaries containing by-product predictions with molecular weights,
        sorted by prediction scores (highest first)
    """
    if not smiles or not smiles.strip():
        return []
    
    try:
        # API endpoint and payload
        url = "https://dev-wmt-inference.di.wuxiapptec.com/api/fmt-inference"
        payload = {
            "smiles": [smiles.strip()],
            "model_name": "davinci_amp_0.3_fmt_inchify_nosorted",
            "top_k": top_k,
            "return_scores": True
        }
        
        headers = {
            "accept": "application/json",
            "Content-Type": "application/json"
        }
        
        logger.info(f"Calling FMT API for by-products prediction: {smiles}")
        
        # Make API call
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        response.raise_for_status()
        
        api_results = response.json()
        
        # Debug: Print the full API response
        logger.info(f"=== BY-PRODUCTS API RESPONSE DEBUG ===")
        logger.info(f"Input SMILES: {smiles}")
        logger.info(f"API Response Status Code: {response.status_code}")
        logger.info(f"Full API Response JSON:")
        logger.info(json.dumps(api_results, indent=2))
        logger.info(f"=== END API RESPONSE DEBUG ===")
        
        # Also print to console for immediate visibility
        print(f"\n🔍 BY-PRODUCTS API DEBUG for {smiles}:")
        print(f"Status: {response.status_code}")
        print(f"Response JSON:")
        print(json.dumps(api_results, indent=2))
        print("=" * 50)
        
        if not api_results or not isinstance(api_results, list) or len(api_results) == 0:
            logger.warning(f"No results returned from API for {smiles}")
            return []
        
        # Extract predictions from the first result
        first_result = api_results[0]
        if "results" not in first_result:
            logger.warning(f"No 'results' key in API response for {smiles}")
            return []
        
        predictions = first_result["results"]
        
        # Process each prediction
        by_products = []
        for pred in predictions:
            pred_smiles = pred.get("predictions", "")
            score = pred.get("scores", 0.0)
            
            if pred_smiles:
                # Calculate molecular weight using RDKit
                mol_weight = calculate_mass(pred_smiles)
                
                by_product_data = {
                    'smiles': pred_smiles,
                    'score': round(score, 6),
                    'molecular_weight': mol_weight,
                    'mz_ESI_pos': calculate_mz(mol_weight, "ESI+") if mol_weight else None,
                    'mz_ESI_neg': calculate_mz(mol_weight, "ESI-") if mol_weight else None,
                    'mz_EI': calculate_mz(mol_weight, "EI") if mol_weight else None,
                    'has_chlorine': has_halogen(pred_smiles)[0],
                    'has_bromine': has_halogen(pred_smiles)[1],
                    'adducts': calc_all_adducts(pred_smiles) if mol_weight else {},
                    'svg': smiles_to_svg(pred_smiles)
                }
                
                by_products.append(by_product_data)
        
        # Sort by score (highest first)
        by_products.sort(key=lambda x: x['score'], reverse=True)
        
        # Debug: Print the processed by-products data
        logger.info(f"=== PROCESSED BY-PRODUCTS DEBUG ===")
        logger.info(f"Input SMILES: {smiles}")
        logger.info(f"Number of by-products: {len(by_products)}")
        logger.info(f"Processed by-products data:")
        logger.info(json.dumps(by_products, indent=2))
        logger.info(f"=== END PROCESSED BY-PRODUCTS DEBUG ===")
        
        # Also print to console for immediate visibility
        print(f"\n📊 PROCESSED BY-PRODUCTS for {smiles}:")
        print(f"Count: {len(by_products)}")
        print(f"Processed Data:")
        print(json.dumps(by_products, indent=2))
        print("=" * 50)
        
        logger.info(f"Successfully predicted {len(by_products)} by-products for {smiles}")
        return by_products
        
    except requests.exceptions.RequestException as e:
        logger.error(f"API request failed for {smiles}: {e}")
        return []
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse API response for {smiles}: {e}")
        return []
    except Exception as e:
        logger.error(f"Error predicting by-products for {smiles}: {e}")
        return []

def predict_carbonylation_by_products(reaction_smiles: str, reagents_smi: List[str] = None, reagents_name: List[str] = None) -> List[Dict]:
    """
    Predict by-products for carbonylation reactions using the carbimp API.
    
    Args:
        reaction_smiles: Reaction SMILES string (substrate>>product format)
        reagents_smi: List of reagent SMILES (optional)
        reagents_name: List of reagent names (optional)
        
    Returns:
        List of dictionaries containing by-product predictions with roles and molecular weights,
        excluding substrate (底物) and main product (主产物)
    """
    if not reaction_smiles or not reaction_smiles.strip():
        return []
    
    try:
        # API endpoint and payload
        url = "https://dev-chemoinf-service.di.wuxiapptec.com/carbimp"
        
        # Prepare payload
        payload = {
            'smi': reaction_smiles.strip(),
            'rgsmi': reagents_smi or [],
            'rgname': reagents_name or []
        }
        
        headers = {
            "Content-Type": "application/json; charset=UTF-8"
        }
        
        logger.info(f"Calling carbonylation API for reaction: {reaction_smiles}")
        logger.info(f"Payload being sent: {json.dumps(payload, indent=2)}")
        
        # Make API call
        response = requests.post(url, data=json.dumps(payload), headers=headers, timeout=30)
        
        # Log response details before raising for status
        logger.info(f"Response status code: {response.status_code}")
        logger.info(f"Response headers: {dict(response.headers)}")
        if response.status_code != 200:
            logger.error(f"Response text: {response.text}")
        
        response.raise_for_status()
        
        api_results = response.json()
        
        # Debug: Print the full API response
        logger.info(f"=== CARBONYLATION API RESPONSE DEBUG ===")
        logger.info(f"Input reaction SMILES: {reaction_smiles}")
        logger.info(f"API Response Status Code: {response.status_code}")
        logger.info(f"Full API Response JSON:")
        logger.info(json.dumps(api_results, indent=2))
        logger.info(f"=== END CARBONYLATION API RESPONSE DEBUG ===")
        
        # Also print to console for immediate visibility
        print(f"\n🔍 CARBONYLATION API DEBUG for {reaction_smiles}:")
        print(f"Status: {response.status_code}")
        print(f"Response JSON:")
        print(json.dumps(api_results, indent=2))
        print("=" * 50)
        
        if not api_results or 'matrix' not in api_results:
            logger.warning(f"No matrix results returned from carbonylation API for {reaction_smiles}")
            return []
        
        matrix = api_results['matrix']
        if not isinstance(matrix, list):
            logger.warning(f"Matrix is not a list in carbonylation API response for {reaction_smiles}")
            return []
        
        # Check if the API returned an error message
        if len(matrix) == 0 and 'msg' in api_results:
            error_msg = api_results['msg']
            logger.warning(f"Carbonylation API returned error for {reaction_smiles}: {error_msg}")
            print(f"\n⚠️  CARBONYLATION API MESSAGE for {reaction_smiles}:")
            print(f"API Message: {error_msg}")
            print("=" * 50)
            return []
        
        # Filter out substrate (底物) and main product (主产物), keep only by-products
        by_products = []
        for item in matrix:
            role = item.get('role', '')
            smiles = item.get('smi', '')
            mw = item.get('mw', 0)
            
            # Skip substrate and main product
            if role in ['底物', '主产物']:
                continue
                
            if smiles and role:
                # Calculate additional properties using existing functions
                by_product_data = {
                    'smiles': smiles,
                    'role': role,
                    'molecular_weight': float(mw) if mw else calculate_mass(smiles),
                    'mz_ESI_pos': calculate_mz(float(mw) if mw else calculate_mass(smiles), "ESI+"),
                    'mz_ESI_neg': calculate_mz(float(mw) if mw else calculate_mass(smiles), "ESI-"),
                    'mz_EI': calculate_mz(float(mw) if mw else calculate_mass(smiles), "EI"),
                    'has_chlorine': has_halogen(smiles)[0],
                    'has_bromine': has_halogen(smiles)[1],
                    'adducts': calc_all_adducts(smiles) if mw or calculate_mass(smiles) else {},
                    'svg': smiles_to_svg(smiles)
                }
                
                by_products.append(by_product_data)
        
        # Debug: Print the processed by-products data
        logger.info(f"=== PROCESSED CARBONYLATION BY-PRODUCTS DEBUG ===")
        logger.info(f"Input reaction SMILES: {reaction_smiles}")
        logger.info(f"Number of by-products: {len(by_products)}")
        logger.info(f"Processed by-products data:")
        logger.info(json.dumps(by_products, indent=2))
        logger.info(f"=== END PROCESSED CARBONYLATION BY-PRODUCTS DEBUG ===")
        
        # Also print to console for immediate visibility
        print(f"\n📊 PROCESSED CARBONYLATION BY-PRODUCTS for {reaction_smiles}:")
        print(f"Count: {len(by_products)}")
        print(f"Processed Data:")
        print(json.dumps(by_products, indent=2))
        print("=" * 50)
        
        logger.info(f"Successfully predicted {len(by_products)} carbonylation by-products for {reaction_smiles}")
        return by_products
        
    except requests.exceptions.RequestException as e:
        logger.error(f"Carbonylation API request failed for {reaction_smiles}: {e}")
        print(f"\n❌ CARBONYLATION API ERROR for {reaction_smiles}:")
        print(f"Request failed: {e}")
        print("=" * 50)
        return []
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse carbonylation API response for {reaction_smiles}: {e}")
        print(f"\n❌ CARBONYLATION JSON ERROR for {reaction_smiles}:")
        print(f"JSON decode failed: {e}")
        print("=" * 50)
        return []
    except Exception as e:
        logger.error(f"Error predicting carbonylation by-products for {reaction_smiles}: {e}")
        print(f"\n❌ CARBONYLATION GENERAL ERROR for {reaction_smiles}:")
        print(f"General error: {e}")
        print("=" * 50)
        return []

def calculate_by_products_data(smiles_list: List[str], top_k: int = 10) -> Dict[str, List[Dict]]:
    """
    Calculate by-products data for a list of SMILES using FMT model.
    
    Args:
        smiles_list: List of SMILES strings
        top_k: Number of top by-products to predict for each SMILES
        
    Returns:
        Dict mapping SMILES to list of by-product predictions
    """
    results = {}
    
    for smiles in smiles_list:
        if not smiles or not smiles.strip():
            continue
            
        smiles = smiles.strip()
        by_products = predict_by_products(smiles, top_k)
        results[smiles] = by_products
    
    return results

def calculate_carbonylation_by_products_data(smiles_list: List[str], reagents_smi: List[str] = None, reagents_name: List[str] = None) -> Dict[str, List[Dict]]:
    """
    Calculate carbonylation by-products data for a list of reaction SMILES.
    
    Args:
        smiles_list: List of reaction SMILES strings
        reagents_smi: List of reagent SMILES (optional)
        reagents_name: List of reagent names (optional)
        
    Returns:
        Dict mapping reaction SMILES to list of by-product predictions
    """
    results = {}
    
    for smiles in smiles_list:
        if not smiles or not smiles.strip():
            continue
            
        smiles = smiles.strip()
        by_products = predict_carbonylation_by_products(smiles, reagents_smi, reagents_name)
        results[smiles] = by_products
    
    return results 