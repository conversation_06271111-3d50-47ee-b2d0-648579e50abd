"""
SMILES reaction parser module for MS calculator.
Parses reaction SMILES into reactants, reagents, and products.
"""

from typing import Tuple, List
from .validator import validate_smiles_list
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def split_smiles(reaction_smiles: str) -> Tuple[List[str], List[str], List[str], List[str]]:
    """
    Parse a reaction SMILES string into components.
    
    Supports reaction arrows: ">>" (standard), ">" (alternative)
    Molecules are separated by "."
    
    Args:
        reaction_smiles (str): Reaction SMILES string
        
    Returns:
        Tuple[List[str], List[str], List[str], List[str]]: 
        (reactants, reagents, products, invalid_smiles)
    """
    if not reaction_smiles or not reaction_smiles.strip():
        return [], [], [], []
    
    reaction_smiles = reaction_smiles.strip()
    
    # Check for reaction arrows
    if ">>" in reaction_smiles:
        # Standard reaction arrow: reactants>>products or reactants>reagents>products
        parts = reaction_smiles.split(">>")
        if len(parts) == 2:
            left_side, products_str = parts
            # Check if left side contains reagents (indicated by >)
            if ">" in left_side:
                reactants_str, reagents_str = left_side.split(">", 1)
            else:
                reactants_str, reagents_str = left_side, ""
        else:
            # Invalid format
            logger.warning(f"Invalid reaction format: {reaction_smiles}")
            return [], [], [], [reaction_smiles]
    
    elif ">" in reaction_smiles:
        # Alternative format: reactants>products or reactants>reagents>products
        parts = reaction_smiles.split(">")
        if len(parts) == 2:
            reactants_str, products_str = parts
            reagents_str = ""
        elif len(parts) == 3:
            reactants_str, reagents_str, products_str = parts
        else:
            # Invalid format
            logger.warning(f"Invalid reaction format: {reaction_smiles}")
            return [], [], [], [reaction_smiles]
    
    else:
        # No reaction arrow, treat as single molecule or mixture
        reactants_str = reaction_smiles
        reagents_str = ""
        products_str = ""
    
    # Split components by "." and clean up
    reactants = [s.strip() for s in reactants_str.split(".") if s.strip()] if reactants_str else []
    reagents = [s.strip() for s in reagents_str.split(".") if s.strip()] if reagents_str else []
    products = [s.strip() for s in products_str.split(".") if s.strip()] if products_str else []
    
    # Validate all SMILES
    all_smiles = reactants + reagents + products
    valid_smiles, invalid_smiles = validate_smiles_list(all_smiles)
    
    # Filter out invalid SMILES from each category
    valid_reactants = [s for s in reactants if s in valid_smiles]
    valid_reagents = [s for s in reagents if s in valid_smiles]
    valid_products = [s for s in products if s in valid_smiles]
    
    logger.info(f"Parsed reaction: {len(valid_reactants)} reactants, {len(valid_reagents)} reagents, "
                f"{len(valid_products)} products, {len(invalid_smiles)} invalid")
    
    return valid_reactants, valid_reagents, valid_products, invalid_smiles


def parse_molecule_list(smiles_string: str) -> Tuple[List[str], List[str]]:
    """
    Parse a string of molecules separated by dots.
    
    Args:
        smiles_string (str): String containing molecules separated by "."
        
    Returns:
        Tuple[List[str], List[str]]: (valid_molecules, invalid_molecules)
    """
    if not smiles_string or not smiles_string.strip():
        return [], []
    
    molecules = [s.strip() for s in smiles_string.split(".") if s.strip()]
    return validate_smiles_list(molecules) 


def preprocess_smiles_for_fmt(smiles: str) -> str:
    """
    Preprocess a single SMILES/reaction SMILES string for the FMT by-products API.

    Rules:
    - When there is no '>', return the input smiles unchanged
    - When there is only one '>' or exactly one '>>', return the substring before the first '>'
    - When there are two '>' (reactants>reagents>products), return
      "reactants.reagents" (substring before first '>' + '.' + substring between two '>')
    - When there are more than two '>' in a single smiles, raise ValueError

    Args:
        smiles (str): Input SMILES or reaction SMILES

    Returns:
        str: Preprocessed SMILES string suitable for FMT API
    """
    if smiles is None:
        return ""
    s = smiles.strip()
    if not s:
        return ""

    gt_count = s.count('>')

    # No arrow at all
    if gt_count == 0:
        return s

    # Exactly one '>>' and no other '>'
    if '>>' in s and gt_count == 2:
        return s.split('>', 1)[0].strip()

    # Only single '>' arrows present
    if '>>' not in s:
        parts = s.split('>')
        if len(parts) == 2:
            # reactants>products
            return parts[0].strip()
        if len(parts) == 3:
            # reactants>reagents>products -> reactants.reagents
            return f"{parts[0].strip()}.{parts[1].strip()}"
        # Any other case means more than two '>'
        raise ValueError("More than two '>' in SMILES")

    # If we reach here, there is at least one '>>' and additional '>' characters
    # That means more than exactly one '>>'
    raise ValueError("More than two '>' in SMILES")