"""
SMILES validation module for MS calculator.
Validates SMILES strings using RDKit.
"""

from typing import List, <PERSON><PERSON>
from rdkit import Chem
from rdkit.Chem import Descriptors
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def is_valid(smiles: str) -> bool:
    """
    Validate a SMILES string using RDKit.
    
    Args:
        smiles (str): SMILES string to validate
        
    Returns:
        bool: True if valid, False otherwise
    """
    if not smiles or not smiles.strip():
        return False
    
    try:
        mol = Chem.MolFromSmiles(smiles.strip())
        if mol is None:
            return False
        
        # Additional validation: check if we can calculate basic properties
        _ = Descriptors.ExactMolWt(mol)
        return True
        
    except Exception as e:
        logger.debug(f"SMILES validation failed for '{smiles}': {e}")
        return False


def validate_smiles_list(smiles_list: List[str]) -> Tu<PERSON>[List[str], List[str]]:
    """
    Validate a list of SMILES strings.
    
    Args:
        smiles_list (list[str]): List of SMILES strings to validate
        
    Returns:
        tuple[list[str], list[str]]: (valid_smiles, invalid_smiles)
    """
    valid = []
    invalid = []
    
    for smiles in smiles_list:
        if smiles.strip():  # Skip empty strings
            if is_valid(smiles):
                valid.append(smiles.strip())
            else:
                invalid.append(smiles.strip())
    
    return valid, invalid 