"""
Flask web application for MS calculator.
Provides web interface and REST API for mass spectrometry calculations.
Enhanced with common ESI adduct ions.
"""

import logging
import json
from flask import Flask, render_template, request, jsonify, flash, redirect, url_for
from werkzeug.exceptions import BadRequest

from core.validator import validate_smiles_list
from core.parser import split_smiles, preprocess_smiles_for_fmt
from core.calculator import calculate_batch_data, calculate_data, get_adduct_info, calc_all_adducts, calculate_by_products_data, calculate_carbonylation_by_products_data

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__, template_folder='ui/templates')
app.secret_key = 'ms_calculator_secret_key_2024'  # Change in production

@app.route('/')
def index():
    """Main page with input form."""
    return render_template('index.html')

@app.route('/adducts')
def adducts_info():
    """Page showing available adducts and their masses."""
    adduct_data = get_adduct_info()
    return render_template('adducts.html', adduct_data=adduct_data)

@app.route('/calculate', methods=['POST'])
def calculate():
    """
    Process form submission and return results.
    Handles both web form and JSON API requests.
    """
    logger.info("Calculate route called")
    try:
        # Get input data
        if request.is_json:
            data = request.get_json()
            smiles_input = data.get('smiles', '')
            show_adducts = data.get('show_adducts', False)
            reaction_type = data.get('reaction_type', 'none')
        else:
            smiles_input = request.form.get('smiles_input', '')
            show_adducts = request.form.get('show_adducts') == 'on'
            reaction_type = request.form.get('reaction_type', 'none')
        
        logger.info(f"Received SMILES input: '{smiles_input}', show_adducts: {show_adducts}, reaction_type: {reaction_type}")
        
        if not smiles_input:
            logger.warning("No SMILES input provided")
            if request.is_json:
                return jsonify({'error': 'No SMILES input provided'}), 400
            flash('Please enter SMILES string(s)', 'error')
            return redirect(url_for('index'))
        
        # Parse SMILES input
        reactants, reagents, products, invalid = split_smiles(smiles_input)
        
        # Calculate data for all categories
        calculation_results = calculate_batch_data(reactants, reagents, products)
        
        results = {
            'reactants': calculation_results['reactants'],
            'reagents': calculation_results['reagents'], 
            'products': calculation_results['products'],
            'invalid': invalid
        }
        
        # Calculate by-products based on reaction type
        if reaction_type == '其他反应':
            # Use FMT model for other reactions, preprocess each input line before calling API
            raw_lines = [line.strip() for line in smiles_input.split('\n') if line.strip()]
            processed_for_fmt = []
            fmt_error = None
            for line in raw_lines:
                try:
                    processed_for_fmt.append(preprocess_smiles_for_fmt(line))
                except ValueError as ex:
                    fmt_error = str(ex)
                    break

            if fmt_error:
                logger.warning(f"FMT preprocessing error: {fmt_error}")
                flash(f"By-products (AI) input error: {fmt_error}", 'error')
            elif processed_for_fmt:
                logger.info(f"=== FMT BY-PRODUCTS CALCULATION START ===")
                logger.info(f"Predicting by-products for {len(processed_for_fmt)} compounds using FMT model: {processed_for_fmt}")
                by_products_results = calculate_by_products_data(processed_for_fmt)
                logger.info(f"FMT by-products calculation completed. Results keys: {list(by_products_results.keys())}")
                logger.info(f"=== FMT BY-PRODUCTS RESULTS DEBUG ===")
                logger.info(json.dumps(by_products_results, indent=2, default=str))
                logger.info(f"=== END FMT BY-PRODUCTS CALCULATION ===")
                results['by_products'] = by_products_results
                results['by_products_type'] = 'fmt'
        elif reaction_type == '插羰反应':
            # Use carbonylation API for carbonylation reactions
            # For carbonylation, we need reaction SMILES (substrate>>product format)
            # The API takes the full reaction SMILES string without parsing
            reaction_smiles_list = []
            # Split input by lines and find lines containing reaction arrows
            for line in smiles_input.split('\n'):
                line = line.strip()
                if line and '>>' in line:
                    reaction_smiles_list.append(line)
            
            if reaction_smiles_list:
                logger.info(f"=== CARBONYLATION BY-PRODUCTS CALCULATION START ===")
                logger.info(f"Predicting carbonylation by-products for {len(reaction_smiles_list)} reactions: {reaction_smiles_list}")
                # For now, use default reagents - could be made configurable later
                reagents_name = ['MeOH', 'DMF', 'HFIP', 'TMEDA']
                by_products_results = calculate_carbonylation_by_products_data(reaction_smiles_list, reagents_name=reagents_name)
                logger.info(f"Carbonylation by-products calculation completed. Results keys: {list(by_products_results.keys())}")
                logger.info(f"=== CARBONYLATION BY-PRODUCTS RESULTS DEBUG ===")
                logger.info(json.dumps(by_products_results, indent=2, default=str))
                logger.info(f"=== END CARBONYLATION BY-PRODUCTS CALCULATION ===")
                
                # Check if any by-products were found
                total_by_products = sum(len(bp_list) for bp_list in by_products_results.values())
                if total_by_products == 0:
                    flash('No by-products found. The reaction may not be a carbonylation reaction or may not produce detectable by-products.', 'warning')
                
                results['by_products'] = by_products_results
                results['by_products_type'] = 'carbonylation'
            else:
                logger.warning("No reaction SMILES found for carbonylation analysis (need substrate>>product format)")
                flash('For carbonylation analysis, please provide reaction SMILES in substrate>>product format', 'error')
        
        # Add enhanced adduct information if requested
        if show_adducts:
            for category in ['reactants', 'reagents', 'products']:
                category_data = results.get(category, {})
                if isinstance(category_data, dict):
                    for smiles, molecule_data in category_data.items():
                        if smiles and isinstance(molecule_data, dict):
                            molecule_data['all_adducts'] = calc_all_adducts(smiles, 'both')
        
        if request.is_json:
            return jsonify(results)
        
        # For web interface, render results template
        total_compounds = len(results['reactants']) + len(results['reagents']) + len(results['products'])
        by_products_dict = results.get('by_products', {})
        by_products_count = sum(len(bp_list) for bp_list in by_products_dict.values()) if isinstance(by_products_dict, dict) else 0
        logger.info(f"Rendering template with {total_compounds} total results and {by_products_count} by-products predictions")
        
        # Debug: Print results structure before template rendering
        logger.info("=== TEMPLATE RENDERING DEBUG ===")
        logger.info(f"Results keys: {list(results.keys())}")
        for key, value in results.items():
            logger.info(f"results['{key}'] type: {type(value)}")
            if key != 'by_products' and isinstance(value, dict):
                logger.info(f"results['{key}'] keys: {list(value.keys())}")
        logger.info("=== END TEMPLATE RENDERING DEBUG ===")
        
        return render_template('index.html', 
                             results=results, 
                             input_smiles=smiles_input,
                             show_adducts=show_adducts,
                             reaction_type=reaction_type)
        
    except Exception as e:
        import traceback
        logger.error(f"Error in calculate route: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        if request.is_json:
            return jsonify({'error': str(e)}), 500
        flash(f'Error processing request: {e}', 'error')
        # Preserve the input data when there's an error by getting it directly from request
        try:
            error_smiles_input = request.form.get('smiles_input', '') if not request.is_json else ''
            error_show_adducts = request.form.get('show_adducts') == 'on' if not request.is_json else False
            error_reaction_type = request.form.get('reaction_type', 'none') if not request.is_json else 'none'
        except:
            error_smiles_input = ''
            error_show_adducts = False
            error_reaction_type = 'none'
        
        return render_template('index.html', 
                             input_smiles=error_smiles_input,
                             show_adducts=error_show_adducts,
                             reaction_type=error_reaction_type)

@app.route('/api/calculate', methods=['POST'])
def api_calculate():
    """
    REST API endpoint for mass calculations.
    
    Expected JSON format:
    {
        "smiles": "CCO>>CC(=O)O",
        "show_adducts": true,
        "reaction_type": "插羰反应" or "其他反应" or "none"
    }
    
    Returns JSON with calculated masses and m/z values.
    """
    try:
        if not request.is_json:
            return jsonify({'error': 'Content-Type must be application/json'}), 400
        
        data = request.get_json()
        smiles_input = data.get('smiles', '')
        show_adducts = data.get('show_adducts', False)
        reaction_type = data.get('reaction_type', 'none')
        
        if not smiles_input:
            return jsonify({'error': 'SMILES input is required'}), 400
        
        # Parse and calculate
        reactants, reagents, products, invalid = split_smiles(smiles_input)
        
        results = {
            'input': smiles_input,
            'parsed': {
                'reactants': reactants,
                'reagents': reagents, 
                'products': products,
                'invalid': invalid
            },
            'calculations': {
                'reactants': calculate_data(reactants) if reactants else {},
                'reagents': calculate_data(reagents) if reagents else {},
                'products': calculate_data(products) if products else {}
            }
        }
        
        # Add by-products prediction based on reaction type
        if reaction_type == '其他反应':
            raw_lines = [line.strip() for line in smiles_input.split('\n') if line.strip()]
            processed_for_fmt = []
            try:
                for line in raw_lines:
                    processed_for_fmt.append(preprocess_smiles_for_fmt(line))
            except ValueError as ex:
                return jsonify({'error': f"By-products (AI) input error: {str(ex)}"}), 400

            if processed_for_fmt:
                logger.info(f"=== API FMT BY-PRODUCTS CALCULATION START ===")
                logger.info(f"API: Predicting by-products for {len(processed_for_fmt)} compounds using FMT: {processed_for_fmt}")
                by_products_results = calculate_by_products_data(processed_for_fmt)
                logger.info(f"API: FMT by-products calculation completed. Results keys: {list(by_products_results.keys())}")
                logger.info(f"=== API FMT BY-PRODUCTS RESULTS DEBUG ===")
                logger.info(json.dumps(by_products_results, indent=2, default=str))
                logger.info(f"=== END API FMT BY-PRODUCTS CALCULATION ===")
                results['by_products'] = by_products_results
                results['by_products_type'] = 'fmt'
        elif reaction_type == '插羰反应':
            reaction_smiles_list = []
            # Split input by lines and find lines containing reaction arrows
            for line in smiles_input.split('\n'):
                line = line.strip()
                if line and '>>' in line:
                    reaction_smiles_list.append(line)
            
            if reaction_smiles_list:
                logger.info(f"=== API CARBONYLATION BY-PRODUCTS CALCULATION START ===")
                logger.info(f"API: Predicting carbonylation by-products for {len(reaction_smiles_list)} reactions: {reaction_smiles_list}")
                reagents_name = ['MeOH', 'DMF', 'HFIP', 'TMEDA']
                by_products_results = calculate_carbonylation_by_products_data(reaction_smiles_list, reagents_name=reagents_name)
                logger.info(f"API: Carbonylation by-products calculation completed. Results keys: {list(by_products_results.keys())}")
                logger.info(f"=== API CARBONYLATION BY-PRODUCTS RESULTS DEBUG ===")
                logger.info(json.dumps(by_products_results, indent=2, default=str))
                logger.info(f"=== END API CARBONYLATION BY-PRODUCTS CALCULATION ===")
                results['by_products'] = by_products_results
                results['by_products_type'] = 'carbonylation'
        
        # Add comprehensive adduct information if requested
        if show_adducts:
            for category in ['reactants', 'reagents', 'products']:
                category_data = results['calculations'].get(category, {})
                if isinstance(category_data, dict):
                    for smiles, molecule_data in category_data.items():
                        if smiles and isinstance(molecule_data, dict):
                            molecule_data['all_adducts'] = calc_all_adducts(smiles, 'both')
        
        return jsonify(results)
        
    except Exception as e:
        logger.error(f"Error in API calculate: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/adducts', methods=['GET'])
def api_adducts():
    """
    API endpoint to get information about available adducts.
    
    Returns JSON with adduct masses and ionization mode information.
    """
    try:
        return jsonify(get_adduct_info())
    except Exception as e:
        logger.error(f"Error in API adducts: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/validate', methods=['POST'])
def api_validate():
    """
    API endpoint to validate SMILES strings.
    
    Expected JSON format:
    {
        "smiles": ["CCO", "CC(=O)O", "invalid"]
    }
    
    Returns validation results.
    """
    try:
        if not request.is_json:
            return jsonify({'error': 'Content-Type must be application/json'}), 400
        
        data = request.get_json()
        smiles_list = data.get('smiles', [])
        
        if not isinstance(smiles_list, list):
            return jsonify({'error': 'SMILES must be provided as a list'}), 400
        
        valid, invalid = validate_smiles_list(smiles_list)
        
        return jsonify({
            'valid': valid,
            'invalid': invalid,
            'total': len(smiles_list),
            'valid_count': len(valid),
            'invalid_count': len(invalid)
        })
        
    except Exception as e:
        logger.error(f"Error in API validate: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/health')
def health():
    """Health check endpoint."""
    return jsonify({'status': 'healthy', 'service': 'MS Calculator'})

@app.errorhandler(404)
def not_found(error):
    """Handle 404 errors."""
    if request.path.startswith('/api/'):
        return jsonify({'error': 'API endpoint not found'}), 404
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors."""
    if request.path.startswith('/api/'):
        return jsonify({'error': 'Internal server error'}), 500
    return render_template('500.html'), 500

if __name__ == '__main__':
    import os

    # Get configuration from environment variables or use defaults
    debug_mode = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    host = os.getenv('FLASK_HOST', '0.0.0.0')
    port = int(os.getenv('FLASK_PORT', '7001'))

    print("Starting MS Calculator web application...")
    print(f"Access the application at: http://localhost:{port}")
    print(f"API documentation available at: http://localhost:{port}/api/adducts")
    print(f"Debug mode: {debug_mode}")

    # Run the Flask server
    app.run(debug=debug_mode, host=host, port=port)