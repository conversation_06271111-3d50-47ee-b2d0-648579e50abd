1. 插羰反应：

url = 'https://dev-chemoinf-service.di.wuxiapptec.com/carbimp'
smi = 'ClC1=CC2=C(C=CN2CC#N)C=N1>>COC(=O)C1=NC=C2C=CN(CC#N)C2=C1'    # 反应式 SMILES，可以只包含底物>>产物
reagents_smi = []    # 试剂、溶剂、催化剂等的SMILES列表，如果不清楚名称，可以用这个传入结构
reagents_name = ['MeOH', 'DMF', 'HFIP', 'TMEDA']    # 或者使用试剂、溶剂的名称（常规名称、命名都可以）

headers = {"Content-Type": "application/json; charset=UTF-8"}    # 使用 JSON 格式传值
data = {'smi':smi, 'rgsmi':reagents_smi, 'rgname':reagents_name}
result = requests.post(url, data=json.dumps(data),headers=headers).json()
# 返回JSON格式的数据，top key：'matrix'：
# {'matrix':[{'role':ROLE_NAME, 'smi':ROLE_MOL_SMILES, 'mw': AVG_ROLE_MOL_WEIGHT}, ...]}
# 每个元素包含最少三项：
#    'role'：底物、主产物或杂质产生的名称（参考鹏宇提示的杂质产生机制的名称）
#    'smi' 和 'mw'：对应分子的结构（SMILES）及其平均分子量
# 注：如果role是底物，会包含其中可能引起杂质产生的具体因素

An example of returned by_products / roles:
- role : 底物
  smi  : N#CCn1ccc2cnc(Cl)cc21
  mw   : 191.621 Da

- role : 主产物
  smi  : COC(=O)c1cc2c(ccn2CC#N)cn1
  mw   : 215.212 Da

- role : 脱卤素
  smi  : N#CCn1ccc2cnccc21
  mw   : 157.176 Da

- role : 水取代
  smi  : N#CCn1ccc2cnc(O)cc21
  mw   : 173.175 Da

- role : MeOH取代
  smi  : COc1cc2c(ccn2CC#N)cn1
  mw   : 187.202 Da

- role : 二甲胺取代
  smi  : CN(C)c1cc2c(ccn2CC#N)cn1
  mw   : 200.245 Da
...


2. 其他反应（using DAVINCIRS FMT model）:

curl -X 'POST' \
  'https://dev-wmt-inference.di.wuxiapptec.com/api/fmt-inference   ' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/json' \
  -d '{
  "smiles": [
    "ClC1=CC2=C(C=CN2CC#N)C=N1"
  ],
  "model_name": "davinci_amp_0.3_fmt_inchify_nosorted",
  "top_k": 10,
  "return_scores": true
}'

Respons body:

[
  {
    "smiles": "ClC1=CC2=C(C=CN2CC#N)C=N1",
    "results": [
      {
        "predictions": "NCCn1ccc2cnc(Cl)cc21",
        "scores": 0.208895
      },
      {
        "predictions": "O=C(O)Cn1ccc2cnc(Cl)cc21",
        "scores": 0.141447
      },
      {
        "predictions": "COC(=O)Cn1ccc2cnc(Cl)cc21",
        "scores": 0.141348
      },
      {
        "predictions": "COC(=N)Cn1ccc2cnc(Cl)cc21",
        "scores": 0.033707
      },
      {
        "predictions": "CCOC(=O)Cn1ccc2cnc(Cl)cc21",
        "scores": 0.029014
      },
      {
        "predictions": "CCOC(=N)Cn1ccc2cnc(Cl)cc21",
        "scores": 0.024311
      },
      {
        "predictions": "N#CCN1CCc2cnc(Cl)cc21",
        "scores": 0.020983
      },
      {
        "predictions": "N=C(O)Cn1ccc2cnc(Cl)cc21",
        "scores": 0.015149
      },
      {
        "predictions": "COc1cc2c(ccn2CC#N)cn1",
        "scores": 0.013317
      },
      {
        "predictions": "NCCn1ccc2cnc(Cl)cc21",
        "scores": 0.013041
      }
    ]
  }
]


3. 其他反应（using DeepSeek）: