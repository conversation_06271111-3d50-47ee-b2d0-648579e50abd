# Use official Python base image
# FROM python:3.11-slim
FROM harbor.di.wuxiapptec.com/public/python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies needed for RDKit
RUN apt-get update && apt-get install -y \
    build-essential \
    libboost-dev \
    libboost-system-dev \
    libboost-filesystem-dev \
    libboost-thread-dev \
    libboost-serialization-dev \
    libboost-python-dev \
    libboost-regex-dev \
    libcairo2-dev \
    libeigen3-dev \
    && rm -rf /var/lib/apt/lists/*

# Upgrade pip
RUN python -m pip install --upgrade pip

# Install Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Expose Flask port
EXPOSE 7001

# Environment
ENV PYTHONUNBUFFERED=1
ENV FLASK_ENV=production

# Default command
CMD ["python", "app.py"]
