@echo off
echo Starting MS Calculator Application in Development Mode...
echo.

REM Activate virtual environment if it exists
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
) else (
    echo No virtual environment found. Using system Python.
)

REM Set environment variables for development
set FLASK_DEBUG=True
set FLASK_HOST=127.0.0.1
set FLASK_PORT=7001

REM Start the application
echo Starting Flask application in debug mode...
python app.py

REM Keep the window open if there's an error
if errorlevel 1 (
    echo.
    echo Application encountered an error. Press any key to exit.
    pause >nul
)
