@echo off
echo Starting MS Calculator Application in Development Mode...
echo.

REM Activate MS Calculator virtual environment if it exists
if exist "ms_calc_venv\Scripts\activate.bat" (
    echo Activating MS Calculator virtual environment...
    call ms_calc_venv\Scripts\activate.bat
) else (
    echo No MS Calculator virtual environment found. Run check_requirements.bat first.
    echo Press any key to exit.
    pause >nul
    exit /b 1
)

REM Set environment variables for development
set FLASK_DEBUG=True
set FLASK_HOST=127.0.0.1
set FLASK_PORT=8080

REM Start the application
echo Starting Flask application in debug mode...
python app.py

REM Keep the window open if there's an error
if errorlevel 1 (
    echo.
    echo Application encountered an error. Press any key to exit.
    pause >nul
)
