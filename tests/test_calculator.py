"""
Unit tests for the calculator module.
"""

import pytest
import sys
import os

# Add the parent directory to the path to import core modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.calculator import (
    calculate_mass, calculate_mz, calc_all_adducts, 
    calculate_data, calculate_batch_data,
    ADDUCT_MASSES
)


class TestCalculateMass:
    """Test cases for calculate_mass function."""
    
    def test_valid_molecules(self):
        """Test mass calculation for valid molecules."""
        # Test known masses (approximate values)
        assert calculate_mass("CCO") == 46.0  # ethanol
        assert calculate_mass("CC(=O)O") == 60.0  # acetic acid
        assert calculate_mass("O") == 18.0  # water
        assert calculate_mass("C") == 16.0  # methane (CH4)
    
    def test_invalid_smiles(self):
        """Test mass calculation for invalid SMILES."""
        assert calculate_mass("INVALID") is None
        assert calculate_mass("X") is None
        assert calculate_mass("C(C") is None
    
    def test_empty_input(self):
        """Test mass calculation for empty input."""
        assert calculate_mass("") is None
        assert calculate_mass("   ") is None
    
    def test_complex_molecules(self):
        """Test mass calculation for complex molecules."""
        # Benzene
        mass = calculate_mass("c1ccccc1")
        assert mass is not None
        assert isinstance(mass, float)
        assert mass > 0
        
        # Caffeine
        mass = calculate_mass("CN1C=NC2=C1C(=O)N(C(=O)N2C)C")
        assert mass is not None
        assert isinstance(mass, float)
        assert mass > 0


class TestCalculateMz:
    """Test cases for calculate_mz function."""
    
    def test_esi_positive(self):
        """Test ESI+ ionization."""
        mass = calculate_mass("CCO")  # ethanol, ~46.0
        mz_esi_pos = calculate_mz(mass, "ESI+")
        
        assert mz_esi_pos is not None
        assert abs(mz_esi_pos - (mass + ADDUCT_MASSES['H+'])) < 0.1
    
    def test_esi_negative(self):
        """Test ESI- ionization."""
        mass = calculate_mass("CCO")  # ethanol, ~46.0
        mz_esi_neg = calculate_mz(mass, "ESI-")
        
        assert mz_esi_neg is not None
        assert abs(mz_esi_neg - (mass + ADDUCT_MASSES['H-'])) < 0.1
    
    def test_ei_ionization(self):
        """Test EI ionization."""
        mass = calculate_mass("CCO")  # ethanol, ~46.0
        mz_ei = calculate_mz(mass, "EI")
        
        assert mz_ei is not None
        assert abs(mz_ei - (mass + ADDUCT_MASSES['e-'])) < 0.1
    
    def test_invalid_mode(self):
        """Test invalid ionization mode."""
        mass = calculate_mass("CCO")
        assert calculate_mz(mass, "INVALID_MODE") is None
    
    def test_invalid_mass(self):
        """Test m/z calculation for invalid mass."""
        assert calculate_mz(None, "ESI+") is None
        assert calculate_mz(-1, "ESI-") is None
        assert calculate_mz(0, "EI") is None
    
    def test_all_modes(self):
        """Test all ionization modes for a molecule."""
        mass = calculate_mass("CCO")
        
        mz_esi_pos = calculate_mz(mass, "ESI+")
        mz_esi_neg = calculate_mz(mass, "ESI-")
        mz_ei = calculate_mz(mass, "EI")
        
        assert all(mz is not None for mz in [mz_esi_pos, mz_esi_neg, mz_ei])
        assert mz_esi_pos > mz_ei  # ESI+ should be higher
        assert mz_esi_neg < mz_ei  # ESI- should be lower


class TestCalcAllAdducts:
    """Test cases for calc_all_adducts function."""
    
    def test_valid_molecule_both_modes(self):
        """Test calculating all adducts for a valid molecule."""
        results = calc_all_adducts("CCO", "both")
        
        # Should have both positive and negative adducts
        assert len(results) > 5
        assert "[M+H]+" in results
        assert "[M-H]-" in results
        assert "[M+Na]+" in results
        assert "[M+K]+" in results
        assert all(mz is not None for mz in results.values())
    
    def test_positive_mode_only(self):
        """Test calculating positive mode adducts only."""
        results = calc_all_adducts("CCO", "positive")
        
        assert "[M+H]+" in results
        assert "[M+Na]+" in results
        assert "[M+K]+" in results
        assert "[M-H]-" not in results
    
    def test_negative_mode_only(self):
        """Test calculating negative mode adducts only."""
        results = calc_all_adducts("CCO", "negative")
        
        assert "[M-H]-" in results
        assert "[M+Cl]-" in results
        assert "[M+H]+" not in results
    
    def test_invalid_molecule(self):
        """Test calculating adducts for an invalid molecule."""
        results = calc_all_adducts("INVALID", "both")
        assert len(results) == 0


class TestCalculateData:
    """Test cases for calculate_data function."""
    
    def test_valid_molecules(self):
        """Test calculating data for valid molecules."""
        smiles_list = ["CCO", "CC(=O)O"]
        results = calculate_data(smiles_list)
        
        assert len(results) == 2
        assert "CCO" in results
        assert "CC(=O)O" in results
        
        for smiles, data in results.items():
            assert data["mass"] is not None
            assert data["mz_ESI_pos"] is not None
            assert data["mz_ESI_neg"] is not None
            assert data["mz_EI"] is not None
            assert isinstance(data["has_chlorine"], bool)
            assert isinstance(data["has_bromine"], bool)
    
    def test_mixed_validity(self):
        """Test calculating data for mixed valid/invalid molecules."""
        smiles_list = ["CCO", "INVALID", "CC(=O)O"]
        results = calculate_data(smiles_list)
        
        assert len(results) == 3
        assert results["CCO"]["mass"] is not None
        assert results["INVALID"]["mass"] is None
        assert results["CC(=O)O"]["mass"] is not None
    
    def test_empty_list(self):
        """Test calculating data for empty list."""
        results = calculate_data([])
        assert len(results) == 0
    
    def test_empty_strings_filtered(self):
        """Test that empty strings are filtered out."""
        smiles_list = ["CCO", "", "   ", "CC(=O)O"]
        results = calculate_data(smiles_list)
        
        assert len(results) == 2
        assert "CCO" in results
        assert "CC(=O)O" in results


class TestCalculateBatchData:
    """Test cases for calculate_batch_data function."""
    
    def test_valid_batch(self):
        """Test calculating data for reactants, reagents, and products."""
        reactants = ["CCO"]
        reagents = ["O"]
        products = ["CC(=O)O"]
        
        results = calculate_batch_data(reactants, reagents, products)
        
        assert "reactants" in results
        assert "reagents" in results
        assert "products" in results
        
        assert len(results["reactants"]) == 1
        assert len(results["reagents"]) == 1
        assert len(results["products"]) == 1
        
        assert "CCO" in results["reactants"]
        assert "O" in results["reagents"]
        assert "CC(=O)O" in results["products"]
    
    def test_empty_categories(self):
        """Test calculating data with some empty categories."""
        reactants = ["CCO"]
        reagents = []
        products = ["CC(=O)O"]
        
        results = calculate_batch_data(reactants, reagents, products)
        
        assert len(results["reactants"]) == 1
        assert len(results["reagents"]) == 0
        assert len(results["products"]) == 1
    
    def test_all_empty(self):
        """Test calculating data with all empty categories."""
        results = calculate_batch_data([], [], [])
        
        assert len(results["reactants"]) == 0
        assert len(results["reagents"]) == 0
        assert len(results["products"]) == 0


class TestConstants:
    """Test constants used in calculations."""
    
    def test_adduct_masses(self):
        """Test adduct mass constants."""
        assert ADDUCT_MASSES['H+'] == 1.007276
        assert ADDUCT_MASSES['H-'] == -1.007276
        assert isinstance(ADDUCT_MASSES['Na+'], float)
        assert isinstance(ADDUCT_MASSES['K+'], float)


if __name__ == "__main__":
    pytest.main([__file__]) 