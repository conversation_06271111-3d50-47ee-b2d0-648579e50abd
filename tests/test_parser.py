"""
Unit tests for the parser module.
"""

import pytest
import sys
import os

# Add the parent directory to the path to import core modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.parser import split_smiles, parse_molecule_list


class TestSplitSmiles:
    """Test cases for split_smiles function."""
    
    def test_simple_reaction(self):
        """Test simple reaction with >> arrow."""
        reactants, reagents, products, invalid = split_smiles("CCO>>CC(=O)O")
        
        assert reactants == ["CCO"]
        assert reagents == []
        assert products == ["CC(=O)O"]
        assert invalid == []
    
    def test_reaction_with_reagents(self):
        """Test reaction with reagents using > arrow."""
        reactants, reagents, products, invalid = split_smiles("CCO>O>CC(=O)O")
        
        assert reactants == ["CCO"]
        assert reagents == ["O"]
        assert products == ["CC(=O)O"]
        assert invalid == []
    
    def test_multiple_reactants_products(self):
        """Test reaction with multiple reactants and products."""
        reactants, reagents, products, invalid = split_smiles("CCO.CC>>CC(=O)O.C")
        
        assert "CCO" in reactants
        assert "CC" in reactants
        assert reagents == []
        assert "CC(=O)O" in products
        assert "C" in products
        assert invalid == []
    
    def test_single_molecule(self):
        """Test single molecule without reaction arrow."""
        reactants, reagents, products, invalid = split_smiles("CCO")
        
        assert reactants == ["CCO"]
        assert reagents == []
        assert products == []
        assert invalid == []
    
    def test_molecule_mixture(self):
        """Test mixture of molecules without reaction arrow."""
        reactants, reagents, products, invalid = split_smiles("CCO.CC(=O)O.c1ccccc1")
        
        assert "CCO" in reactants
        assert "CC(=O)O" in reactants
        assert "c1ccccc1" in reactants
        assert reagents == []
        assert products == []
        assert invalid == []
    
    def test_alternative_arrow_format(self):
        """Test alternative > arrow format."""
        reactants, reagents, products, invalid = split_smiles("CCO>CC(=O)O")
        
        assert reactants == ["CCO"]
        assert reagents == []
        assert products == ["CC(=O)O"]
        assert invalid == []
    
    def test_invalid_smiles_in_reaction(self):
        """Test reaction containing invalid SMILES."""
        reactants, reagents, products, invalid = split_smiles("CCO.INVALID>>CC(=O)O.X")
        
        assert "CCO" in reactants
        assert "CC(=O)O" in products
        assert "INVALID" in invalid
        assert "X" in invalid
        assert len(reactants) == 1
        assert len(products) == 1
        assert len(invalid) == 2
    
    def test_empty_input(self):
        """Test empty input."""
        reactants, reagents, products, invalid = split_smiles("")
        
        assert reactants == []
        assert reagents == []
        assert products == []
        assert invalid == []
    
    def test_whitespace_input(self):
        """Test whitespace-only input."""
        reactants, reagents, products, invalid = split_smiles("   ")
        
        assert reactants == []
        assert reagents == []
        assert products == []
        assert invalid == []
    
    def test_invalid_reaction_format(self):
        """Test invalid reaction format."""
        reactants, reagents, products, invalid = split_smiles("CCO>>>CC(=O)O")
        
        # The parser will split this and find invalid SMILES
        assert len(invalid) > 0
        assert "CCO" in reactants  # CCO is valid
        # The ">CC(=O)O" part will be invalid due to the extra ">"
    
    def test_complex_reaction(self):
        """Test complex reaction with multiple components."""
        reaction = "CCO.CC(=O)O>N.O>CC(=O)OCC.O"
        reactants, reagents, products, invalid = split_smiles(reaction)
        
        assert "CCO" in reactants
        assert "CC(=O)O" in reactants
        assert "N" in reagents
        assert "O" in reagents
        assert "CC(=O)OCC" in products
        assert "O" in products
        assert invalid == []


class TestParseMoleculeList:
    """Test cases for parse_molecule_list function."""
    
    def test_valid_molecules(self):
        """Test string with valid molecules."""
        valid, invalid = parse_molecule_list("CCO.CC(=O)O.c1ccccc1")
        
        assert len(valid) == 3
        assert len(invalid) == 0
        assert "CCO" in valid
        assert "CC(=O)O" in valid
        assert "c1ccccc1" in valid
    
    def test_mixed_molecules(self):
        """Test string with valid and invalid molecules."""
        valid, invalid = parse_molecule_list("CCO.INVALID.CC(=O)O.X")
        
        assert len(valid) == 2
        assert len(invalid) == 2
        assert "CCO" in valid
        assert "CC(=O)O" in valid
        assert "INVALID" in invalid
        assert "X" in invalid
    
    def test_single_molecule(self):
        """Test string with single molecule."""
        valid, invalid = parse_molecule_list("CCO")
        
        assert valid == ["CCO"]
        assert invalid == []
    
    def test_empty_string(self):
        """Test empty string."""
        valid, invalid = parse_molecule_list("")
        
        assert valid == []
        assert invalid == []
    
    def test_whitespace_string(self):
        """Test whitespace-only string."""
        valid, invalid = parse_molecule_list("   ")
        
        assert valid == []
        assert invalid == []
    
    def test_string_with_empty_components(self):
        """Test string with empty components."""
        valid, invalid = parse_molecule_list("CCO..CC(=O)O")
        
        assert len(valid) == 2
        assert len(invalid) == 0
        assert "CCO" in valid
        assert "CC(=O)O" in valid


if __name__ == "__main__":
    pytest.main([__file__]) 