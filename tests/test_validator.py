"""
Unit tests for the validator module.
"""

import pytest
import sys
import os

# Add the parent directory to the path to import core modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.validator import is_valid, validate_smiles_list


class TestIsValid:
    """Test cases for is_valid function."""
    
    def test_valid_smiles(self):
        """Test valid SMILES strings."""
        valid_smiles = [
            "CCO",  # ethanol
            "CC(=O)O",  # acetic acid
            "c1ccccc1",  # benzene
            "CC(C)C",  # isobutane
            "O",  # water
            "N",  # ammonia
            "C=C",  # ethene
        ]
        
        for smiles in valid_smiles:
            assert is_valid(smiles), f"'{smiles}' should be valid"
    
    def test_invalid_smiles(self):
        """Test invalid SMILES strings."""
        invalid_smiles = [
            "X",  # invalid element
            "C(C",  # unmatched parentheses
            "C(C)(C)(C)(C)(C)C",  # invalid valence
            "CC(C)(C)(C)C",  # invalid valence
            "",  # empty string
            "   ",  # whitespace only
        ]
        
        for smiles in invalid_smiles:
            assert not is_valid(smiles), f"'{smiles}' should be invalid"
    
    def test_edge_cases(self):
        """Test edge cases."""
        assert not is_valid(None)
        assert not is_valid("")
        assert not is_valid("   ")
        assert is_valid("  CCO  ")  # whitespace should be stripped


class TestValidateSmilesList:
    """Test cases for validate_smiles_list function."""
    
    def test_mixed_list(self):
        """Test list with both valid and invalid SMILES."""
        smiles_list = ["CCO", "INVALID", "CC(=O)O", "X", "c1ccccc1"]
        valid, invalid = validate_smiles_list(smiles_list)
        
        assert "CCO" in valid
        assert "CC(=O)O" in valid
        assert "c1ccccc1" in valid
        assert "INVALID" in invalid
        assert "X" in invalid
        assert len(valid) == 3
        assert len(invalid) == 2
    
    def test_all_valid(self):
        """Test list with all valid SMILES."""
        smiles_list = ["CCO", "CC(=O)O", "c1ccccc1"]
        valid, invalid = validate_smiles_list(smiles_list)
        
        assert len(valid) == 3
        assert len(invalid) == 0
        assert all(s in valid for s in smiles_list)
    
    def test_all_invalid(self):
        """Test list with all invalid SMILES."""
        smiles_list = ["INVALID", "X", "C(C"]
        valid, invalid = validate_smiles_list(smiles_list)
        
        assert len(valid) == 0
        assert len(invalid) == 3
        assert all(s in invalid for s in smiles_list)
    
    def test_empty_list(self):
        """Test empty list."""
        valid, invalid = validate_smiles_list([])
        assert len(valid) == 0
        assert len(invalid) == 0
    
    def test_list_with_empty_strings(self):
        """Test list containing empty strings."""
        smiles_list = ["CCO", "", "CC(=O)O", "   ", "c1ccccc1"]
        valid, invalid = validate_smiles_list(smiles_list)
        
        # Empty strings should be skipped
        assert len(valid) == 3
        assert len(invalid) == 0
        assert "CCO" in valid
        assert "CC(=O)O" in valid
        assert "c1ccccc1" in valid


if __name__ == "__main__":
    pytest.main([__file__]) 